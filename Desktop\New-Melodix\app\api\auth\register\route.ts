import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { connectToDatabase } from '../../../lib/db';
import User from '../../../lib/models/User';

const registerSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name cannot be more than 50 characters'),
  lastName: z.string().max(50, 'Last name cannot be more than 50 characters').optional(),
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(30, 'Username cannot be more than 30 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
  email: z.string().email('Please enter a valid email'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = registerSchema.parse(body);

    // Connect to database
    await connectToDatabase();

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { email: validatedData.email.toLowerCase() },
        { username: validatedData.username.toLowerCase() }
      ]
    });

    if (existingUser) {
      if (existingUser.email === validatedData.email.toLowerCase()) {
        return NextResponse.json(
          { error: 'User with this email already exists' },
          { status: 400 }
        );
      }
      if (existingUser.username === validatedData.username.toLowerCase()) {
        return NextResponse.json(
          { error: 'Username is already taken' },
          { status: 400 }
        );
      }
    }

    // Create new user
    const user = new User({
      firstName: validatedData.firstName,
      lastName: validatedData.lastName || '',
      username: validatedData.username.toLowerCase(),
      email: validatedData.email.toLowerCase(),
      password: validatedData.password,
    });

    await user.save();

    // Return success response (without password)
    const { password, ...userWithoutPassword } = user.toObject();
    
    return NextResponse.json(
      { 
        message: 'User created successfully',
        user: userWithoutPassword
      },
      { status: 201 }
    );

  } catch (error: any) {
    console.error('Registration error:', error);

    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    if (error.name === 'ValidationError') {
      const firstError = Object.values(error.errors)[0] as any;
      return NextResponse.json(
        { error: firstError.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
